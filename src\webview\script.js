// YouTube Shorts Panel Script - Simplified Direct Iframe Approach
(function() {
    'use strict';

    // State
    let currentVideoIndex = 0;
    let videos = [];
    let autoScroll = true;
    let autoScrollTimer = null;

    // DOM Elements
    const loading = document.getElementById('loading');
    const videoContainer = document.getElementById('videoContainer');
    const error = document.getElementById('error');
    const videoTitle = document.getElementById('videoTitle');
    const videoChannel = document.getElementById('videoChannel');
    const currentVideoSpan = document.getElementById('currentVideo');
    const totalVideosSpan = document.getElementById('totalVideos');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const autoScrollToggle = document.getElementById('autoScrollToggle');
    const refreshBtn = document.getElementById('refreshBtn');
    const retryBtn = document.getElementById('retryBtn');
    const playerDiv = document.getElementById('player');

    // Initialize
    init();

    function init() {
        console.log('Initializing YouTube Shorts Panel...');
        setupEventListeners();
        loadVideos();
    }

    function setupEventListeners() {
        prevBtn.addEventListener('click', () => previousVideo());
        nextBtn.addEventListener('click', () => nextVideo());
        autoScrollToggle.addEventListener('click', toggleAutoScroll);
        refreshBtn.addEventListener('click', () => loadVideos());
        retryBtn.addEventListener('click', () => loadVideos());
    }

    function loadVideos() {
        console.log('Loading videos...');
        showLoading();

        try {
            // Using YouTube video IDs that work well in embedded players
            const videoData = [
                { id: 'dQw4w9WgXcQ', title: 'Rick Astley - Never Gonna Give You Up', channel: 'Rick Astley' },
                { id: 'kJQP7kiw5Fk', title: 'Luis Fonsi - Despacito ft. Daddy Yankee', channel: 'Luis Fonsi' },
                { id: 'fJ9rUzIMcZQ', title: 'PSY - GANGNAM STYLE', channel: 'officialpsy' },
                { id: 'YQHsXMglC9A', title: 'Adele - Hello', channel: 'Adele' },
                { id: 'JGwWNGJdvx8', title: 'Ed Sheeran - Shape of You', channel: 'Ed Sheeran' },
                { id: 'CevxZvSJLk8', title: 'Katy Perry - Roar', channel: 'Katy Perry' },
                { id: 'hTWKbfoikeg', title: 'Nirvana - Smells Like Teen Spirit', channel: 'Nirvana' },
                { id: 'RgKAFK5djSk', title: 'Shakira - Waka Waka', channel: 'Shakira' },
                { id: 'pRpeEdMmmQ0', title: 'Shakira - Hips Don\'t Lie', channel: 'Shakira' },
                { id: 'jNQXAC9IVRw', title: 'Me at the zoo', channel: 'jawed' }
            ];

            videos = videoData;
            currentVideoIndex = 0;
            totalVideosSpan.textContent = videos.length;

            if (videos.length > 0) {
                createPlayer();
                showVideoContainer();
                startAutoScroll();
            } else {
                showError();
            }
        } catch (err) {
            console.error('Error loading videos:', err);
            showError();
        }
    }

    function createPlayer() {
        const video = videos[currentVideoIndex];
        const videoId = video.id;
        console.log('Creating iframe player for video:', videoId);

        // Create YouTube iframe embed
        const iframe = document.createElement('iframe');
        iframe.width = '100%';
        iframe.height = '100%';
        iframe.src = `https://www.youtube.com/embed/${videoId}?autoplay=1&controls=1&modestbranding=1&rel=0&fs=1&playsinline=1`;
        iframe.frameBorder = '0';
        iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture';
        iframe.allowFullscreen = true;
        iframe.style.border = 'none';
        iframe.style.borderRadius = '8px';

        // Clear and add iframe
        playerDiv.innerHTML = '';
        playerDiv.appendChild(iframe);

        // Update video info
        updateVideoInfo();

        console.log('Iframe player created successfully');
    }

    function startAutoScroll() {
        if (autoScrollTimer) {
            clearInterval(autoScrollTimer);
        }

        if (autoScroll) {
            // Auto-advance every 10 seconds (good for testing, can be adjusted)
            autoScrollTimer = setInterval(() => {
                if (autoScroll) {
                    console.log('Auto-advancing to next video...');
                    nextVideo();
                }
            }, 10000); // 10 seconds per video

            console.log('Auto-scroll started - videos will advance every 10 seconds');
        }
    }

    function stopAutoScroll() {
        if (autoScrollTimer) {
            clearInterval(autoScrollTimer);
            autoScrollTimer = null;
            console.log('Auto-scroll stopped');
        }
    }

    function updateVideoInfo() {
        const video = videos[currentVideoIndex];
        videoTitle.textContent = video.title;
        videoChannel.textContent = video.channel;
        currentVideoSpan.textContent = currentVideoIndex + 1;

        // Update button states - enable infinite scroll when auto-scroll is on
        prevBtn.disabled = false;
        nextBtn.disabled = false;

        // Add visual feedback for current video
        console.log(`Now playing: ${video.title} (${currentVideoIndex + 1}/${videos.length})`);
    }

    function previousVideo() {
        if (currentVideoIndex > 0) {
            currentVideoIndex--;
        } else {
            // Loop to last video (infinite scroll)
            currentVideoIndex = videos.length - 1;
        }
        createPlayer();
        console.log('Previous video:', currentVideoIndex + 1);
    }

    function nextVideo() {
        if (currentVideoIndex < videos.length - 1) {
            currentVideoIndex++;
        } else {
            // Loop back to first video (infinite scroll)
            currentVideoIndex = 0;
        }
        createPlayer();
        console.log('Next video:', currentVideoIndex + 1);
    }

    function toggleAutoScroll() {
        autoScroll = !autoScroll;
        autoScrollToggle.textContent = `🔄 Auto-scroll: ${autoScroll ? 'ON' : 'OFF'}`;
        autoScrollToggle.classList.toggle('active', autoScroll);

        if (autoScroll) {
            startAutoScroll();
        } else {
            stopAutoScroll();
        }

        console.log('Auto-scroll toggled:', autoScroll ? 'ON' : 'OFF');
    }

    function showLoading() {
        loading.style.display = 'flex';
        videoContainer.style.display = 'none';
        error.style.display = 'none';
    }

    function showVideoContainer() {
        loading.style.display = 'none';
        videoContainer.style.display = 'flex';
        error.style.display = 'none';
    }

    function showError() {
        loading.style.display = 'none';
        videoContainer.style.display = 'none';
        error.style.display = 'flex';
    }

    // Message passing to VS Code extension
    function sendMessage(type, text) {
        if (typeof acquireVsCodeApi !== 'undefined') {
            const vscode = acquireVsCodeApi();
            vscode.postMessage({ type, text });
        }
    }

})();

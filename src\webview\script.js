// YouTube Shorts Panel Script
(function() {
    'use strict';

    // Configuration
    const API_KEY = 'YOUR_YOUTUBE_API_KEY'; // You'll need to get this from Google Cloud Console
    const SHORTS_SEARCH_QUERY = 'shorts trending';
    const MAX_RESULTS = 20;

    // State
    let currentVideoIndex = 0;
    let videos = [];
    let player = null;
    let autoScroll = true;
    let isPlayerReady = false;

    // DOM Elements
    const loading = document.getElementById('loading');
    const videoContainer = document.getElementById('videoContainer');
    const error = document.getElementById('error');
    const videoTitle = document.getElementById('videoTitle');
    const videoChannel = document.getElementById('videoChannel');
    const currentVideoSpan = document.getElementById('currentVideo');
    const totalVideosSpan = document.getElementById('totalVideos');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const autoScrollToggle = document.getElementById('autoScrollToggle');
    const refreshBtn = document.getElementById('refreshBtn');
    const retryBtn = document.getElementById('retryBtn');

    // Initialize
    init();

    function init() {
        setupEventListeners();
        loadYouTubeAPI();
    }

    function setupEventListeners() {
        prevBtn.addEventListener('click', () => previousVideo());
        nextBtn.addEventListener('click', () => nextVideo());
        autoScrollToggle.addEventListener('click', toggleAutoScroll);
        refreshBtn.addEventListener('click', () => loadVideos());
        retryBtn.addEventListener('click', () => loadVideos());
    }

    function loadYouTubeAPI() {
        // Load YouTube IFrame API
        const tag = document.createElement('script');
        tag.src = 'https://www.youtube.com/iframe_api';
        const firstScriptTag = document.getElementsByTagName('script')[0];
        firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

        // Set global callback
        window.onYouTubeIframeAPIReady = onYouTubeIframeAPIReady;
    }

    function onYouTubeIframeAPIReady() {
        console.log('YouTube API Ready');
        loadVideos();
    }

    async function loadVideos() {
        showLoading();
        
        try {
            // For demo purposes, we'll use a hardcoded list of YouTube Shorts video IDs
            // In a real implementation, you would use the YouTube Data API
            const demoVideoIds = [
                'dQw4w9WgXcQ', // Rick Roll (classic)
                'jNQXAC9IVRw', // Me at the zoo
                'kJQP7kiw5Fk', // Despacito
                'fJ9rUzIMcZQ', // Gangnam Style
                'RgKAFK5djSk', // Waka Waka
                'pRpeEdMmmQ0', // Shakira
                'CevxZvSJLk8', // Katy Perry
                'hTWKbfoikeg', // Smells Like Teen Spirit
                'YQHsXMglC9A', // Hello - Adele
                'JGwWNGJdvx8'  // Shape of You
            ];

            videos = demoVideoIds.map((id, index) => ({
                id: id,
                title: `YouTube Short #${index + 1}`,
                channelTitle: `Channel ${index + 1}`,
                duration: 'Short'
            }));

            currentVideoIndex = 0;
            totalVideosSpan.textContent = videos.length;
            
            if (videos.length > 0) {
                createPlayer();
                showVideoContainer();
            } else {
                showError();
            }
        } catch (err) {
            console.error('Error loading videos:', err);
            showError();
        }
    }

    function createPlayer() {
        if (player) {
            player.destroy();
        }

        const videoId = videos[currentVideoIndex].id;
        
        player = new YT.Player('player', {
            height: '100%',
            width: '100%',
            videoId: videoId,
            playerVars: {
                autoplay: 1,
                controls: 1,
                modestbranding: 1,
                rel: 0,
                showinfo: 0,
                fs: 0,
                cc_load_policy: 0,
                iv_load_policy: 3,
                autohide: 1
            },
            events: {
                onReady: onPlayerReady,
                onStateChange: onPlayerStateChange,
                onError: onPlayerError
            }
        });
    }

    function onPlayerReady(event) {
        isPlayerReady = true;
        updateVideoInfo();
        console.log('Player ready');
    }

    function onPlayerStateChange(event) {
        // YT.PlayerState.ENDED = 0
        if (event.data === YT.PlayerState.ENDED && autoScroll) {
            setTimeout(() => {
                nextVideo();
            }, 1000); // Wait 1 second before auto-advancing
        }
    }

    function onPlayerError(event) {
        console.error('Player error:', event.data);
        // Try next video on error
        if (autoScroll) {
            nextVideo();
        }
    }

    function updateVideoInfo() {
        const video = videos[currentVideoIndex];
        videoTitle.textContent = video.title;
        videoChannel.textContent = video.channelTitle;
        currentVideoSpan.textContent = currentVideoIndex + 1;
        
        // Update button states
        prevBtn.disabled = currentVideoIndex === 0;
        nextBtn.disabled = currentVideoIndex === videos.length - 1;
    }

    function previousVideo() {
        if (currentVideoIndex > 0) {
            currentVideoIndex--;
            loadCurrentVideo();
        }
    }

    function nextVideo() {
        if (currentVideoIndex < videos.length - 1) {
            currentVideoIndex++;
            loadCurrentVideo();
        }
    }

    function loadCurrentVideo() {
        if (player && isPlayerReady) {
            const videoId = videos[currentVideoIndex].id;
            player.loadVideoById(videoId);
            updateVideoInfo();
        }
    }

    function toggleAutoScroll() {
        autoScroll = !autoScroll;
        autoScrollToggle.textContent = `🔄 Auto-scroll: ${autoScroll ? 'ON' : 'OFF'}`;
        autoScrollToggle.classList.toggle('active', autoScroll);
    }

    function showLoading() {
        loading.style.display = 'flex';
        videoContainer.style.display = 'none';
        error.style.display = 'none';
    }

    function showVideoContainer() {
        loading.style.display = 'none';
        videoContainer.style.display = 'flex';
        error.style.display = 'none';
    }

    function showError() {
        loading.style.display = 'none';
        videoContainer.style.display = 'none';
        error.style.display = 'flex';
    }

    // Message passing to VS Code extension
    function sendMessage(type, text) {
        if (typeof acquireVsCodeApi !== 'undefined') {
            const vscode = acquireVsCodeApi();
            vscode.postMessage({ type, text });
        }
    }

})();

// YouTube Shorts Panel Script
(function() {
    'use strict';

    // Configuration
    const API_KEY = 'YOUR_YOUTUBE_API_KEY'; // You'll need to get this from Google Cloud Console
    const SHORTS_SEARCH_QUERY = 'shorts trending';
    const MAX_RESULTS = 20;

    // State
    let currentVideoIndex = 0;
    let videos = [];
    let player = null;
    let autoScroll = true;
    let isPlayerReady = false;

    // DOM Elements
    const loading = document.getElementById('loading');
    const videoContainer = document.getElementById('videoContainer');
    const error = document.getElementById('error');
    const videoTitle = document.getElementById('videoTitle');
    const videoChannel = document.getElementById('videoChannel');
    const currentVideoSpan = document.getElementById('currentVideo');
    const totalVideosSpan = document.getElementById('totalVideos');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const autoScrollToggle = document.getElementById('autoScrollToggle');
    const refreshBtn = document.getElementById('refreshBtn');
    const retryBtn = document.getElementById('retryBtn');
    const playOverlay = document.getElementById('playOverlay');

    // Initialize
    init();

    function init() {
        setupEventListeners();
        loadYouTubeAPI();
    }

    function setupEventListeners() {
        prevBtn.addEventListener('click', () => previousVideo());
        nextBtn.addEventListener('click', () => nextVideo());
        autoScrollToggle.addEventListener('click', toggleAutoScroll);
        refreshBtn.addEventListener('click', () => loadVideos());
        retryBtn.addEventListener('click', () => loadVideos());

        // Play overlay click handler
        if (playOverlay) {
            playOverlay.addEventListener('click', () => {
                if (player && isPlayerReady) {
                    player.playVideo();
                    playOverlay.style.display = 'none';
                }
            });
        }
    }

    function loadYouTubeAPI() {
        // Check if YouTube API is already loaded
        if (window.YT && window.YT.Player) {
            console.log('YouTube API already loaded');
            onYouTubeIframeAPIReady();
            return;
        }

        // Load YouTube IFrame API
        const tag = document.createElement('script');
        tag.src = 'https://www.youtube.com/iframe_api';
        tag.onerror = function() {
            console.error('Failed to load YouTube API');
            showError();
        };

        const firstScriptTag = document.getElementsByTagName('script')[0];
        firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

        // Set global callback
        window.onYouTubeIframeAPIReady = onYouTubeIframeAPIReady;

        // Fallback timeout
        setTimeout(() => {
            if (!window.YT || !window.YT.Player) {
                console.error('YouTube API failed to load within timeout');
                showError();
            }
        }, 10000);
    }

    function onYouTubeIframeAPIReady() {
        console.log('YouTube API Ready');
        loadVideos();
    }

    async function loadVideos() {
        showLoading();

        try {
            // Using YouTube video IDs that work well in embedded players
            const shortsVideoIds = [
                'dQw4w9WgXcQ', // Rick Astley - Never Gonna Give You Up
                'kJQP7kiw5Fk', // Luis Fonsi - Despacito ft. Daddy Yankee
                'fJ9rUzIMcZQ', // PSY - GANGNAM STYLE
                'YQHsXMglC9A', // Adele - Hello
                'JGwWNGJdvx8', // Ed Sheeran - Shape of You
                'CevxZvSJLk8', // Katy Perry - Roar
                'hTWKbfoikeg', // Nirvana - Smells Like Teen Spirit
                'RgKAFK5djSk', // Shakira - Waka Waka
                'pRpeEdMmmQ0', // Shakira - Hips Don't Lie
                'jNQXAC9IVRw'  // Me at the zoo (first YouTube video)
            ];

            // Fetch video details using YouTube API (if available) or use defaults
            videos = await Promise.all(shortsVideoIds.map(async (id, index) => {
                try {
                    // Try to get real video info
                    const videoInfo = await getVideoInfo(id);
                    return {
                        id: id,
                        title: videoInfo.title || `Trending Short #${index + 1}`,
                        channelTitle: videoInfo.channelTitle || `Creator ${index + 1}`,
                        duration: videoInfo.duration || 'Short',
                        thumbnail: videoInfo.thumbnail || `https://img.youtube.com/vi/${id}/maxresdefault.jpg`
                    };
                } catch (error) {
                    return {
                        id: id,
                        title: `Trending Short #${index + 1}`,
                        channelTitle: `Creator ${index + 1}`,
                        duration: 'Short',
                        thumbnail: `https://img.youtube.com/vi/${id}/maxresdefault.jpg`
                    };
                }
            }));

            currentVideoIndex = 0;
            totalVideosSpan.textContent = videos.length;

            if (videos.length > 0) {
                createPlayer();
                showVideoContainer();
            } else {
                showError();
            }
        } catch (err) {
            console.error('Error loading videos:', err);
            showError();
        }
    }

    async function getVideoInfo(videoId) {
        // If API key is available, fetch real video info
        if (API_KEY && API_KEY !== 'YOUR_YOUTUBE_API_KEY') {
            try {
                const response = await fetch(`https://www.googleapis.com/youtube/v3/videos?part=snippet&id=${videoId}&key=${API_KEY}`);
                const data = await response.json();
                if (data.items && data.items.length > 0) {
                    const snippet = data.items[0].snippet;
                    return {
                        title: snippet.title,
                        channelTitle: snippet.channelTitle,
                        thumbnail: snippet.thumbnails.high.url,
                        duration: 'Short'
                    };
                }
            } catch (error) {
                console.log('API fetch failed, using defaults');
            }
        }

        // Return default info if API is not available
        return {
            title: null,
            channelTitle: null,
            thumbnail: null,
            duration: 'Short'
        };
    }

    function createPlayer() {
        if (player) {
            player.destroy();
        }

        const videoId = videos[currentVideoIndex].id;
        console.log('Creating player for video:', videoId);

        player = new YT.Player('player', {
            height: '100%',
            width: '100%',
            videoId: videoId,
            playerVars: {
                autoplay: 1,           // Auto-play videos
                controls: 1,           // Show player controls
                modestbranding: 1,     // Reduce YouTube branding
                rel: 0,                // Don't show related videos
                showinfo: 0,           // Don't show video info
                fs: 1,                 // Allow fullscreen
                cc_load_policy: 0,     // Don't show captions by default
                iv_load_policy: 3,     // Don't show annotations
                autohide: 1,           // Auto-hide controls
                playsinline: 1,        // Play inline on mobile
                loop: 0,               // Don't loop individual videos
                mute: 0,               // Don't mute by default
                start: 0,              // Start from beginning
                enablejsapi: 1,        // Enable JavaScript API
                origin: window.location.origin
            },
            events: {
                onReady: onPlayerReady,
                onStateChange: onPlayerStateChange,
                onError: onPlayerError
            }
        });
    }

    function onPlayerReady(event) {
        isPlayerReady = true;
        updateVideoInfo();
        console.log('Player ready for video:', videos[currentVideoIndex].id);

        // Try to start playing, show overlay if autoplay is blocked
        try {
            event.target.playVideo();
            // Hide overlay if autoplay works
            setTimeout(() => {
                if (playOverlay && event.target.getPlayerState() === YT.PlayerState.PLAYING) {
                    playOverlay.style.display = 'none';
                }
            }, 1000);
        } catch (error) {
            console.log('Auto-play blocked, showing play overlay');
            if (playOverlay) {
                playOverlay.style.display = 'flex';
            }
        }
    }

    function onPlayerStateChange(event) {
        console.log('Player state changed:', event.data);

        // Handle different player states
        switch (event.data) {
            case YT.PlayerState.ENDED:
                console.log('Video ended, auto-scroll:', autoScroll);
                if (autoScroll) {
                    setTimeout(() => {
                        nextVideo();
                    }, 500); // Shorter delay for better Shorts experience
                }
                break;
            case YT.PlayerState.PLAYING:
                console.log('Video playing');
                if (playOverlay) {
                    playOverlay.style.display = 'none';
                }
                break;
            case YT.PlayerState.PAUSED:
                console.log('Video paused');
                break;
            case YT.PlayerState.BUFFERING:
                console.log('Video buffering');
                break;
        }
    }

    function onPlayerError(event) {
        console.error('Player error:', event.data);

        // Handle different error types
        switch (event.data) {
            case 2:
                console.error('Invalid video ID');
                break;
            case 5:
                console.error('HTML5 player error');
                break;
            case 100:
                console.error('Video not found or private');
                break;
            case 101:
            case 150:
                console.error('Video not allowed in embedded players');
                break;
        }

        // Try next video on error after a short delay
        setTimeout(() => {
            if (autoScroll && currentVideoIndex < videos.length - 1) {
                nextVideo();
            } else {
                showError();
            }
        }, 2000);
    }

    function updateVideoInfo() {
        const video = videos[currentVideoIndex];
        videoTitle.textContent = video.title;
        videoChannel.textContent = video.channelTitle;
        currentVideoSpan.textContent = currentVideoIndex + 1;

        // Update button states - disable only if not in auto-scroll mode
        prevBtn.disabled = !autoScroll && currentVideoIndex === 0;
        nextBtn.disabled = !autoScroll && currentVideoIndex === videos.length - 1;

        // Add visual feedback for current video
        console.log(`Now playing: ${video.title} (${currentVideoIndex + 1}/${videos.length})`);
    }

    function previousVideo() {
        if (currentVideoIndex > 0) {
            currentVideoIndex--;
            loadCurrentVideo();
        } else if (autoScroll) {
            // Loop to last video if auto-scroll is on
            currentVideoIndex = videos.length - 1;
            loadCurrentVideo();
        }
    }

    function nextVideo() {
        if (currentVideoIndex < videos.length - 1) {
            currentVideoIndex++;
            loadCurrentVideo();
        } else if (autoScroll) {
            // Loop back to first video if auto-scroll is on (infinite scroll)
            currentVideoIndex = 0;
            loadCurrentVideo();
        }
    }

    function loadCurrentVideo() {
        if (player && isPlayerReady && videos[currentVideoIndex]) {
            const videoId = videos[currentVideoIndex].id;
            console.log('Loading video:', videoId, 'Index:', currentVideoIndex);

            try {
                player.loadVideoById({
                    videoId: videoId,
                    startSeconds: 0
                });
                updateVideoInfo();
            } catch (error) {
                console.error('Error loading video:', error);
                // Try to recreate player if loading fails
                setTimeout(() => {
                    createPlayer();
                }, 1000);
            }
        } else if (!isPlayerReady) {
            // If player is not ready, wait and try again
            setTimeout(() => {
                loadCurrentVideo();
            }, 500);
        }
    }

    function toggleAutoScroll() {
        autoScroll = !autoScroll;
        autoScrollToggle.textContent = `🔄 Auto-scroll: ${autoScroll ? 'ON' : 'OFF'}`;
        autoScrollToggle.classList.toggle('active', autoScroll);
    }

    function showLoading() {
        loading.style.display = 'flex';
        videoContainer.style.display = 'none';
        error.style.display = 'none';
    }

    function showVideoContainer() {
        loading.style.display = 'none';
        videoContainer.style.display = 'flex';
        error.style.display = 'none';
    }

    function showError() {
        loading.style.display = 'none';
        videoContainer.style.display = 'none';
        error.style.display = 'flex';
    }

    // Message passing to VS Code extension
    function sendMessage(type, text) {
        if (typeof acquireVsCodeApi !== 'undefined') {
            const vscode = acquireVsCodeApi();
            vscode.postMessage({ type, text });
        }
    }

})();

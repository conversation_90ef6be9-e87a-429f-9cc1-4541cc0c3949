{"name": "youtube-shorts-panel", "displayName": "YouTube Shorts Panel", "description": "A VS Code extension that displays YouTube Shorts in a side panel with auto-scroll functionality", "version": "0.0.1", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "youtube-shorts-panel.openPanel", "title": "Open YouTube Shorts Panel", "category": "YouTube Shorts"}], "views": {"youtube-shorts": [{"id": "youtubeShortsPanel", "name": "<PERSON><PERSON> Feed", "type": "webview"}]}, "viewsContainers": {"activitybar": [{"id": "youtube-shorts", "title": "YouTube Shorts", "icon": "$(play)"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "package": "vsce package", "test": "npm run compile && echo 'Run F5 in VS Code to test the extension'"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.0"}}
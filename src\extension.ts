import * as vscode from 'vscode';
import { YoutubeShortsProvider } from './webview/youtubeShortsProvider';

export function activate(context: vscode.ExtensionContext) {
    console.log('YouTube Shorts Panel extension is now active!');

    // Create the webview provider
    const provider = new YoutubeShortsProvider(context.extensionUri);

    // Register the webview provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('youtubeShortsPanel', provider)
    );

    // Register command to open panel
    const openPanelCommand = vscode.commands.registerCommand('youtube-shorts-panel.openPanel', () => {
        vscode.commands.executeCommand('workbench.view.extension.youtube-shorts');
    });

    context.subscriptions.push(openPanelCommand);

    // Set context to show the view
    vscode.commands.executeCommand('setContext', 'youtubeShortsPanel.visible', true);
}

export function deactivate() {
    console.log('YouTube Shorts Panel extension is now deactivated!');
}

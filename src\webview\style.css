body {
    font-family: var(--vscode-font-family);
    color: var(--vscode-foreground);
    background-color: var(--vscode-editor-background);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

.container {
    padding: 10px;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.header h2 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.refresh-btn {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
}

.refresh-btn:hover {
    background: var(--vscode-button-hoverBackground);
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--vscode-progressBar-background);
    border-top: 4px solid var(--vscode-progressBar-foreground);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.video-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.video-info {
    margin-bottom: 10px;
}

.video-info h3 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.video-info p {
    margin: 0;
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
}

.player-wrapper {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 177.78%; /* 9:16 aspect ratio for shorts */
    margin-bottom: 15px;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.player-wrapper #player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: opacity 0.3s ease;
}

.play-overlay:hover {
    background: rgba(0, 0, 0, 0.8);
}

.play-button {
    font-size: 48px;
    margin-bottom: 10px;
    animation: pulse 2s infinite;
}

.play-overlay p {
    color: white;
    font-size: 14px;
    margin: 0;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.controls {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.control-btn {
    background: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
    border: 1px solid var(--vscode-button-border);
    padding: 6px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    flex: 1;
    min-width: 80px;
    transition: all 0.2s;
}

.control-btn:hover {
    background: var(--vscode-button-secondaryHoverBackground);
}

.control-btn.active {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.progress {
    text-align: center;
    font-size: 12px;
    color: var(--vscode-descriptionForeground);
    padding: 8px;
    background: var(--vscode-editor-inactiveSelectionBackground);
    border-radius: 4px;
}

.error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    text-align: center;
}

.error p {
    margin-bottom: 15px;
    color: var(--vscode-errorForeground);
}

.retry-btn {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.retry-btn:hover {
    background: var(--vscode-button-hoverBackground);
}

/* Responsive adjustments */
@media (max-width: 300px) {
    .controls {
        flex-direction: column;
    }

    .control-btn {
        min-width: unset;
    }
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.YoutubeShortsProvider = void 0;
const vscode = require("vscode");
class YoutubeShortsProvider {
    constructor(_extensionUri) {
        this._extensionUri = _extensionUri;
    }
    resolveWebviewView(webviewView, context, _token) {
        this._view = webviewView;
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._extensionUri
            ]
        };
        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);
        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(message => {
            switch (message.type) {
                case 'error':
                    vscode.window.showErrorMessage(message.text);
                    break;
                case 'info':
                    vscode.window.showInformationMessage(message.text);
                    break;
            }
        }, undefined, []);
    }
    _getHtmlForWebview(webview) {
        // Get the local path to main script run in the webview, then convert it to a uri we can use in the webview.
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'src', 'webview', 'script.js'));
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'src', 'webview', 'style.css'));
        // Use a nonce to only allow a specific script to be run.
        const nonce = getNonce();
        return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}' https://www.youtube.com https://www.googleapis.com https://s.ytimg.com; frame-src https://www.youtube.com https://www.youtube-nocookie.com; connect-src https://www.googleapis.com https://www.youtube.com; img-src https://img.youtube.com https://i.ytimg.com data: ${webview.cspSource};">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <link href="${styleUri}" rel="stylesheet">
                <title>YouTube Shorts</title>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h2>🎬 YouTube Shorts</h2>
                        <button id="refreshBtn" class="refresh-btn">🔄 Refresh</button>
                    </div>
                    <div class="loading" id="loading">
                        <div class="spinner"></div>
                        <p>Loading YouTube Shorts...</p>
                    </div>
                    <div class="video-container" id="videoContainer" style="display: none;">
                        <div class="video-info">
                            <h3 id="videoTitle">Loading...</h3>
                            <p id="videoChannel">Loading...</p>
                        </div>
                        <div class="player-wrapper">
                            <div id="player"></div>
                            <div class="play-overlay" id="playOverlay" style="display: none;">
                                <div class="play-button">▶️</div>
                                <p>Click to play</p>
                            </div>
                        </div>
                        <div class="controls">
                            <button id="prevBtn" class="control-btn">⏮️ Previous</button>
                            <button id="nextBtn" class="control-btn">⏭️ Next</button>
                            <button id="autoScrollToggle" class="control-btn active">🔄 Auto-scroll: ON</button>
                        </div>
                        <div class="progress">
                            <span id="currentVideo">1</span> / <span id="totalVideos">10</span>
                        </div>
                    </div>
                    <div class="error" id="error" style="display: none;">
                        <p>❌ Failed to load YouTube Shorts</p>
                        <button id="retryBtn" class="retry-btn">Try Again</button>
                    </div>
                </div>
                <script nonce="${nonce}" src="${scriptUri}"></script>
            </body>
            </html>`;
    }
}
exports.YoutubeShortsProvider = YoutubeShortsProvider;
YoutubeShortsProvider.viewType = 'youtubeShortsPanel';
function getNonce() {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
}
//# sourceMappingURL=youtubeShortsProvider.js.map
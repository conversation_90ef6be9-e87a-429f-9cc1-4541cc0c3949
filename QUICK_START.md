# 🚀 Quick Start Guide - YouTube Shorts Panel Extension

## ⚡ Fast Setup (2 minutes)

### 1. Install Dependencies
```bash
npm install
```

### 2. Compile TypeScript
```bash
npm run compile
```

### 3. Test the Extension
1. Press `F5` in VS Code
2. In the new window, click the ▶️ icon in the Activity Bar
3. Enjoy YouTube Shorts in your sidebar! 🎬

## 🎯 What You'll See

- **Side Panel**: YouTube Shorts player in VS Code sidebar
- **Auto-scroll**: Videos automatically advance when they end
- **Controls**: Previous/Next buttons and auto-scroll toggle
- **Demo Videos**: 10 popular YouTube videos for testing

## 🔧 Customization

### Add Your YouTube API Key (Optional)
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable YouTube Data API v3
3. Create an API key
4. Replace `YOUR_YOUTUBE_API_KEY` in `src/webview/script.js`

### Change Video Selection
Edit the `demoVideoIds` array in `src/webview/script.js` with your preferred video IDs.

## 🎮 Controls

| Button | Function |
|--------|----------|
| ⏮️ Previous | Go to previous video |
| ⏭️ Next | Go to next video |
| 🔄 Auto-scroll | Toggle automatic advancement |
| 🔄 Refresh | Reload video list |

## 🐛 Troubleshooting

**Panel not showing?**
- Look for ▶️ icon in Activity Bar
- Use Command Palette: "YouTube Shorts: Open YouTube Shorts Panel"

**Videos not playing?**
- Check internet connection
- Open Developer Tools (Help > Toggle Developer Tools)

## 📦 Package for Distribution

```bash
npm install -g vsce
npm run package
```

This creates a `.vsix` file you can install or share.

## 🎉 You're Ready!

Your VS Code extension is now ready to use. Press `F5` to start testing and enjoy YouTube Shorts while coding! 

For detailed information, see `README.md` and `test-extension.md`.

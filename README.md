# YouTube Shorts Panel - VS Code Extension

A VS Code extension that displays YouTube Shorts in a side panel with auto-scroll functionality.

## Features

- 🎬 **YouTube Shorts Integration**: Display trending YouTube Shorts directly in VS Code
- 🔄 **Auto-scroll**: Automatically advance to the next short when the current one ends
- ⏮️ **Manual Navigation**: Previous/Next buttons for manual control
- 🎮 **Playback Controls**: Toggle auto-scroll on/off
- 📱 **Responsive Design**: Optimized for VS Code's side panel
- 🌙 **Theme Support**: Adapts to VS Code's current theme

## Installation & Setup

### Prerequisites
- VS Code 1.74.0 or higher
- Node.js 16.x or higher
- npm or yarn

### Development Setup

1. **Clone/Download the extension files**
2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Compile TypeScript**:
   ```bash
   npm run compile
   ```

4. **Run the extension**:
   - Press `F5` in VS Code to open a new Extension Development Host window
   - Or use the "Run Extension" configuration in the Debug panel

### Optional: YouTube API Setup

For live YouTube Shorts data (instead of demo videos):

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the YouTube Data API v3
4. Create credentials (API Key)
5. Replace `YOUR_YOUTUBE_API_KEY` in `src/webview/script.js` with your API key

## Usage

1. **Open the Panel**:
   - Click the YouTube Shorts icon in the Activity Bar
   - Or use Command Palette: `YouTube Shorts: Open YouTube Shorts Panel`

2. **Controls**:
   - **Auto-scroll Toggle**: Enable/disable automatic advancement
   - **Previous/Next**: Manual navigation between shorts
   - **Refresh**: Reload the shorts feed

3. **Auto-scroll Behavior**:
   - When enabled, automatically moves to the next short when current one ends
   - Can be toggled on/off at any time
   - 1-second delay between videos for smooth transition

## Project Structure

```
youtube-shorts-panel/
├── src/
│   ├── extension.ts              # Main extension entry point
│   └── webview/
│       ├── youtubeShortsProvider.ts  # Webview provider
│       ├── script.js             # Frontend JavaScript
│       └── style.css             # Styling
├── .vscode/
│   └── launch.json               # Debug configuration
├── package.json                  # Extension manifest
├── tsconfig.json                 # TypeScript config
└── README.md                     # This file
```

## Development

### Building
```bash
npm run compile
```

### Watching for changes
```bash
npm run watch
```

### Testing
1. Press `F5` to launch Extension Development Host
2. Open Command Palette (`Ctrl+Shift+P`)
3. Run "YouTube Shorts: Open YouTube Shorts Panel"

## Technical Details

- **Framework**: VS Code Extension API with Webview
- **Language**: TypeScript + JavaScript
- **Video Player**: YouTube IFrame API
- **Styling**: CSS with VS Code theme variables
- **API**: YouTube Data API v3 (optional)

## Limitations

- Currently uses demo video IDs (requires YouTube API key for live data)
- Requires internet connection for video playback
- Subject to YouTube's embedding policies
- Auto-scroll timing may vary based on video length

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Troubleshooting

### Common Issues

1. **Videos not loading**:
   - Check internet connection
   - Verify YouTube API key (if using live data)
   - Check browser console for errors

2. **Extension not appearing**:
   - Ensure VS Code version is 1.74.0+
   - Try reloading the Extension Development Host
   - Check the Output panel for errors

3. **Auto-scroll not working**:
   - Ensure auto-scroll is enabled (green button)
   - Check if video actually ended
   - Some videos may have different end detection

### Debug Mode

Enable debug logging by opening Developer Tools in the Extension Development Host:
- `Help` > `Toggle Developer Tools`
- Check Console tab for detailed logs

## Future Enhancements

- [ ] Instagram Reels support (if API becomes available)
- [ ] Custom video categories/tags
- [ ] Playlist support
- [ ] Video quality selection
- [ ] Keyboard shortcuts
- [ ] Video history/favorites
- [ ] Search functionality

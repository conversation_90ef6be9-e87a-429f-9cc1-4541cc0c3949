"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const youtubeShortsProvider_1 = require("./webview/youtubeShortsProvider");
function activate(context) {
    console.log('YouTube Shorts Panel extension is now active!');
    // Create the webview provider
    const provider = new youtubeShortsProvider_1.YoutubeShortsProvider(context.extensionUri);
    // Register the webview provider
    context.subscriptions.push(vscode.window.registerWebviewViewProvider('youtubeShortsPanel', provider));
    // Register command to open panel
    const openPanelCommand = vscode.commands.registerCommand('youtube-shorts-panel.openPanel', () => {
        vscode.commands.executeCommand('workbench.view.extension.youtube-shorts');
    });
    context.subscriptions.push(openPanelCommand);
    // Set context to show the view
    vscode.commands.executeCommand('setContext', 'youtubeShortsPanel.visible', true);
}
exports.activate = activate;
function deactivate() {
    console.log('YouTube Shorts Panel extension is now deactivated!');
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map